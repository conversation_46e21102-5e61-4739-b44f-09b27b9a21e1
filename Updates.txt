# Exam Timezone Management

## Overview
EduFair exam system uses UTC (Coordinated Universal Time) for all exam scheduling to ensure consistent timing across different geographical locations.

## How It Works

### Backend (Automatic)
- All exam start times are stored in UTC format in the database
- When updating exam times, any timezone input is automatically converted to UTC
- API responses always return times in UTC format

### Frontend (Your Responsibility)
- Detect user's timezone using browser API or IP geolocation
- Convert user's local time to UTC before sending to API
- Convert UTC times from API to user's local time for display

## API Endpoint

### Update Exam Time
```
PUT /api/exams/{exam_id}
```

**Request Body:**
```json
{
  "start_time": "2024-01-15T14:00:00",
  "total_duration": 90
}
```

**Notes:**
- `start_time`: ISO 8601 format, will be converted to UTC if timezone provided
- `total_duration`: Duration in minutes
- Only teachers can update their own exams

## Frontend Implementation

### 1. Detect User Timezone
```javascript
// Get user's timezone
const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
// Example: "Asia/Karachi", "America/New_York", "Europe/London"
```

### 2. Convert Local Time to UTC (Before API Call)
```javascript
function convertToUTC(localDateTime) {
    const date = new Date(localDateTime);
    return date.toISOString().slice(0, 19); // Remove 'Z'
}

// Example usage
const userInput = "2024-01-15T19:00"; // 7 PM Pakistan time
const utcTime = convertToUTC(userInput); // "2024-01-15T14:00:00"
```

### 3. Convert UTC to Local Time (For Display)
```javascript
function convertToLocal(utcDateTime, timezone) {
    const utcDate = new Date(utcDateTime + 'Z');
    return new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    }).format(utcDate);
}

// Example usage
const apiTime = "2024-01-15T14:00:00"; // UTC from API
const localTime = convertToLocal(apiTime, "Asia/Karachi"); // "01/15/2024, 19:00"
```

## Complete Example

### Updating Exam Start Time
```javascript
async function updateExamTime(examId, localStartTime) {
    // Convert local time to UTC
    const utcStartTime = convertToUTC(localStartTime);
    
    // Send to API
    const response = await fetch(`/api/exams/${examId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            start_time: utcStartTime,
            total_duration: 90
        })
    });
    
    return response.json();
}

// Usage
updateExamTime("exam-123", "2024-01-15T19:00"); // User's local time
```

### Displaying Exam Time
```javascript
async function displayExamTime(examId) {
    // Get exam data
    const response = await fetch(`/api/exams/${examId}`);
    const exam = await response.json();
    
    // Convert UTC to user's local time
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const localStartTime = convertToLocal(exam.start_time, userTimezone);
    
    // Display with timezone info
    document.getElementById('exam-start').textContent = 
        `${localStartTime} (${userTimezone})`;
}
```

## Timezone Examples

| User Location | User Input (Local) | Stored (UTC) | Display (Local) |
|---------------|-------------------|--------------|-----------------|
| Pakistan (UTC+5) | 19:00 | 14:00 | 19:00 PKT |
| USA East (UTC-5) | 09:00 | 14:00 | 09:00 EST |
| UK (UTC+0) | 14:00 | 14:00 | 14:00 GMT |
| India (UTC+5:30) | 19:30 | 14:00 | 19:30 IST |

## Important Notes

1. **Always show timezone** to users when displaying times
2. **Test with different timezones** during development
3. **Handle edge cases** like daylight saving time (browser APIs handle this automatically)
4. **Validate user input** before converting to UTC
5. **Use consistent date formats** throughout your application

## Error Handling

```javascript
function safeConvertToUTC(localDateTime) {
    try {
        const date = new Date(localDateTime);
        if (isNaN(date.getTime())) {
            throw new Error('Invalid date format');
        }
        return date.toISOString().slice(0, 19);
    } catch (error) {
        console.error('Date conversion error:', error);
        return null;
    }
}
```

## Summary

- **Backend**: Stores all exam times in UTC
- **Frontend**: Converts between local time and UTC
- **User Experience**: Always shows times in user's local timezone
- **Global Support**: Works for users anywhere in the world

This approach ensures exam scheduling works consistently across all timezones while providing a localized experience for each user.
