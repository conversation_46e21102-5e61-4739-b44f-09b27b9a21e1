import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { FiMail, <PERSON>Lock, <PERSON>Eye, FiEyeOff } from "react-icons/fi";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

import Navbar from "../common/navbar/Navbar";
import video from "../../assets/images/auth/login-video.mp4";
import { loginUser } from "../../store/slices/LoginSlice";

export default function Login() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, status, error } = useSelector((state) => state.login); 

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // Check if user is already authenticated on component mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const role = localStorage.getItem('role');
    
    if (token && role) {
      // Validate token before redirecting
      const validateToken = async () => {
        try {
          await axios.get(`${API_BASE_URL}/api/users/me`);
          
          // Token is valid, redirect to dashboard
          const rolePath = role.toLowerCase();
          navigate(`/${rolePath}/dashboard`);
        } catch (error) {
          // Token is invalid or network error, clear auth data
          localStorage.removeItem('token');
          localStorage.removeItem('role');
          localStorage.removeItem('userdata');
        }
      };

      validateToken();
    }
  }, [navigate]);

  useEffect(() => {
  if (status === "succeeded" && user) {
    const role = user.role;

    if (role === "admin") navigate("/admin/dashboard");
    else if (role === "student") navigate("/student/dashboard");
    else if (role === "sponsor") navigate("/sponsor/dashboard");
    else if (role === "institute") navigate("/institute/dashboard");
    else if (role === "teacher") navigate("/teacher/dashboard");
    else navigate("/Login");
  }
}, [status, user, navigate]);

  const handleSubmit = (e) => {
    e.preventDefault();
    const form = {
      email,
      password,
    };
    dispatch(loginUser(form));
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden bg-white dark:bg-gray-900">
      <Navbar />

      <div className="flex flex-col md:flex-row flex-1 pt-20">
        {/* Left Video Side (desktop only) */}
        <div className="hidden md:flex w-1/2 items-center justify-center bg-black relative">
          <video
            autoPlay
            muted
            loop
            className="absolute w-full h-full object-cover opacity-70"
          >
            <source src={video} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="z-10 text-white text-center p-8">
            <h2 className="text-2xl md:text-3xl font-semibold">Welcome to</h2>
            <h1 className="text-4xl md:text-5xl font-bold mt-2">Edufair Community</h1>
            <p className="mt-4 text-lg">
              Home to many Teachers & Students worldwide
            </p>
            <Link to="#" className="text-violet-400 mt-2 inline-block hover:underline">
              Know more
            </Link>
          </div>
        </div>

        {/* Right Form Side */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-6 bg-white dark:bg-gray-900">
          <div className="w-full max-w-md space-y-6">
            <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100">Welcome Back</h2>
            <p className="text-gray-600 dark:text-gray-400">Login to your EduFair account</p>

            <form className="space-y-4" onSubmit={handleSubmit}>
              {/* Email Field */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-3 flex items-center text-gray-400 dark:text-gray-500">
                    <FiMail />
                  </span>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 mt-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-3 flex items-center text-gray-400 dark:text-gray-500">
                    <FiLock />
                  </span>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full pl-10 pr-10 mt-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="••••••••"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-violet-600 dark:hover:text-violet-400"
                  >
                    {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                  </button>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className={`w-full text-white py-2 rounded-md transition cursor-pointer ${status === 'loading' ? 'bg-violet-300 dark:bg-violet-400 cursor-not-allowed' : 'bg-violet-600 hover:bg-violet-700 dark:bg-violet-500 dark:hover:bg-violet-600'
                  }`}
                disabled={status === 'loading'}
              >
                {status === 'loading' ? 'Logging in...' : 'Login'}
              </button>

            </form>

            {error && (
              <p className="text-red-600 dark:text-red-400 text-sm mt-2 border border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                {error}
              </p>
            )}


            <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
              Don't have an account?{" "}
              <Link to="/Signup-Menu" className="text-violet-600 dark:text-violet-400 hover:underline">
                Sign up here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
