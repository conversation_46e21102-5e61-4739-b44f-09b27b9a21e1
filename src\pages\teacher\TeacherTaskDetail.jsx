import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTaskById,
  fetchTaskSubmissions,
  selectCurrentTask,
  selectTasksLoading,
  selectTasksError,
  selectSubmissions,
  selectSubmissionLoading,
  clearTaskState
} from '../../store/slices/TaskSlice';
import TaskAttachments from '../../components/task/TaskAttachments';
import TaskGrading from '../../components/task/TaskGrading';
import {
  FiArrowLeft,
  FiCalendar,
  FiClock,
  FiUsers,
  FiBook,
  FiEdit3,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle,
  FiSend,
  FiEye,
  FiDownload
} from 'react-icons/fi';

/**
 * TeacherTaskDetail Page
 * Detailed view of a task for teachers with submission management and grading
 */
const TeacherTaskDetail = () => {
  const { taskId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const task = useSelector(selectCurrentTask);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);
  const submissions = useSelector(selectSubmissions);
  const submissionLoading = useSelector(selectSubmissionLoading);

  // Local state
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load task and submissions on mount
  useEffect(() => {
    if (taskId) {
      dispatch(fetchTaskById(taskId));
      dispatch(fetchTaskSubmissions({ task_id: taskId }));
    }

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch, taskId]);

  // Auto-select first submission when submissions load
  useEffect(() => {
    if (submissions.length > 0 && !selectedSubmission) {
      setSelectedSubmission(submissions[0]);
    }
  }, [submissions, selectedSubmission]);

  // Calculate submission statistics
  const submissionStats = {
    total: submissions.length,
    submitted: submissions.filter(s => s.status === 'submitted' || s.status === 'graded').length,
    graded: submissions.filter(s => s.status === 'graded').length,
    pending: submissions.filter(s => s.status === 'submitted').length
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      submitted: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        icon: FiSend,
        text: 'Submitted'
      },
      graded: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        icon: FiCheckCircle,
        text: 'Graded'
      },
      not_submitted: {
        color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        icon: FiClock,
        text: 'Not Submitted'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    );
  };

  if (loading && !task) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
          <p className={`text-sm ${textSecondary}`}>Loading task details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <FiAlertCircle className={`w-8 h-8 mx-auto mb-2 text-red-500`} />
          <p className={`text-sm ${textSecondary}`}>{error}</p>
          <button
            onClick={() => navigate('/teacher/tasks')}
            className="mt-4 text-blue-600 hover:text-blue-700 underline"
          >
            Back to Tasks
          </button>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <p className={`text-sm ${textSecondary}`}>Task not found</p>
          <button
            onClick={() => navigate('/teacher/tasks')}
            className="mt-4 text-blue-600 hover:text-blue-700 underline"
          >
            Back to Tasks
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/teacher/tasks')}
          className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${textSecondary}`}
        >
          <FiArrowLeft className="w-5 h-5" />
        </button>
        
        <div className="flex-1">
          <h1 className={`text-2xl font-bold ${textPrimary}`}>{task.name}</h1>
          <p className={`mt-1 text-sm ${textSecondary}`}>
            Task Details and Submission Management
          </p>
        </div>

        <button
          onClick={() => navigate(`/teacher/tasks/${taskId}/edit`)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <FiEdit3 className="w-4 h-4" />
          Edit Task
        </button>
      </div>

      {/* Tabs */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor} mb-6`}>
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'overview', label: 'Overview', icon: FiBook },
            { id: 'submissions', label: `Submissions (${submissionStats.total})`, icon: FiSend },
            { id: 'grading', label: 'Grading', icon: FiCheckCircle }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : `border-transparent ${textSecondary} hover:text-gray-700 dark:hover:text-gray-300`
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Task Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-3">
                  <FiBook className={`w-5 h-5 ${textSecondary}`} />
                  <div>
                    <p className={`text-sm ${textSecondary}`}>Subject</p>
                    <p className={`font-medium ${textPrimary}`}>{task.subject || 'No Subject'}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <FiCalendar className={`w-5 h-5 ${textSecondary}`} />
                  <div>
                    <p className={`text-sm ${textSecondary}`}>Deadline</p>
                    <p className={`font-medium ${textPrimary}`}>
                      {task.deadline 
                        ? new Date(task.deadline).toLocaleString()
                        : 'No deadline'
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <FiUsers className={`w-5 h-5 ${textSecondary}`} />
                  <div>
                    <p className={`text-sm ${textSecondary}`}>Assigned Students</p>
                    <p className={`font-medium ${textPrimary}`}>{task.assigned_students || 0}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <FiClock className={`w-5 h-5 ${textSecondary}`} />
                  <div>
                    <p className={`text-sm ${textSecondary}`}>Created</p>
                    <p className={`font-medium ${textPrimary}`}>
                      {new Date(task.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              {task.description && (
                <div>
                  <h4 className={`text-sm font-medium ${textPrimary} mb-2`}>Description</h4>
                  <div className={`p-4 rounded-lg ${bgSecondary} ${textPrimary} whitespace-pre-wrap`}>
                    {task.description}
                  </div>
                </div>
              )}
            </div>

            {/* Task Attachments */}
            <div>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Files</h3>
              <TaskAttachments
                taskId={task.id}
                attachmentType="task"
                canUpload={true}
                canDelete={true}
                maxFileSize={50}
              />
            </div>
          </div>

          {/* Statistics */}
          <div className="space-y-6">
            {/* Submission Statistics */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Submission Statistics</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className={textSecondary}>Total Students</span>
                  <span className={`font-semibold ${textPrimary}`}>{submissionStats.total}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className={textSecondary}>Submitted</span>
                  <span className={`font-semibold ${textPrimary}`}>{submissionStats.submitted}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className={textSecondary}>Graded</span>
                  <span className={`font-semibold ${textPrimary}`}>{submissionStats.graded}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className={textSecondary}>Pending Review</span>
                  <span className={`font-semibold ${textPrimary}`}>{submissionStats.pending}</span>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span className={textSecondary}>Completion Rate</span>
                  <span className={textSecondary}>
                    {submissionStats.total > 0 
                      ? Math.round((submissionStats.submitted / submissionStats.total) * 100)
                      : 0
                    }%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: submissionStats.total > 0 
                        ? `${(submissionStats.submitted / submissionStats.total) * 100}%`
                        : '0%'
                    }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Quick Actions</h3>
              
              <div className="space-y-3">
                <button
                  onClick={() => setActiveTab('submissions')}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <FiEye className="w-4 h-4" />
                  View Submissions
                </button>
                
                <button
                  onClick={() => setActiveTab('grading')}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                >
                  <FiCheckCircle className="w-4 h-4" />
                  Grade Submissions
                </button>
                
                <button
                  onClick={() => {/* Implement export functionality */}}
                  className={`w-full px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center gap-2 ${textPrimary}`}
                >
                  <FiDownload className="w-4 h-4" />
                  Export Results
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'submissions' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Submissions List */}
          <div className={`${bgPrimary} rounded-lg border ${borderColor}`}>
            <div className={`px-6 py-4 border-b ${borderColor} ${bgSecondary}`}>
              <h3 className={`text-lg font-semibold ${textPrimary}`}>
                Student Submissions ({submissions.length})
              </h3>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {submissionLoading ? (
                <div className="p-6 text-center">
                  <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
                  <p className={`text-sm ${textSecondary}`}>Loading submissions...</p>
                </div>
              ) : submissions.length === 0 ? (
                <div className="p-6 text-center">
                  <FiSend className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
                  <p className={`text-sm ${textSecondary}`}>No submissions yet</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {submissions.map((submission) => (
                    <div
                      key={submission.id}
                      onClick={() => setSelectedSubmission(submission)}
                      className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${
                        selectedSubmission?.id === submission.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className={`font-medium ${textPrimary}`}>
                          {submission.student_name || `Student ${submission.student_id}`}
                        </h4>
                        <StatusBadge status={submission.status} />
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>
                          {submission.submitted_at 
                            ? `Submitted ${new Date(submission.submitted_at).toLocaleDateString()}`
                            : 'Not submitted'
                          }
                        </span>
                        
                        {submission.grade !== null && submission.grade !== undefined && (
                          <span className="font-medium">
                            Grade: {submission.grade}
                            {submission.max_grade && `/${submission.max_grade}`}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Selected Submission Details */}
          <div>
            {selectedSubmission ? (
              <TaskGrading
                task={task}
                submission={selectedSubmission}
                onGradingComplete={() => {
                  // Refresh submissions
                  dispatch(fetchTaskSubmissions({ task_id: taskId }));
                }}
              />
            ) : (
              <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
                <div className="text-center py-8">
                  <FiSend className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
                  <p className={`${textSecondary}`}>Select a submission to view details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'grading' && (
        <div>
          {selectedSubmission ? (
            <TaskGrading
              task={task}
              submission={selectedSubmission}
              onGradingComplete={() => {
                dispatch(fetchTaskSubmissions({ task_id: taskId }));
              }}
            />
          ) : (
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <div className="text-center py-8">
                <FiCheckCircle className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
                <p className={`${textSecondary} mb-4`}>Select a submission from the Submissions tab to start grading</p>
                <button
                  onClick={() => setActiveTab('submissions')}
                  className="text-blue-600 hover:text-blue-700 underline"
                >
                  Go to Submissions
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TeacherTaskDetail;
