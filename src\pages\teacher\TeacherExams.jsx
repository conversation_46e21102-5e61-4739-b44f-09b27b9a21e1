import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import { getTeacherExams, deleteExam, fetchTeacherExamsList, resetTeacherExamsList } from '../../store/slices/ExamSlice';
import ExamTable from '../../components/ui/tables/ExamTable';
import { LoadMoreButton } from '../../components/ui/Pagination';
import { 
  FiPlus, 
  FiSearch, 
  FiFilter, 
  FiRefreshCw,
  FiFileText,
  FiCalendar,
  FiUsers,
  FiTrendingUp
} from 'react-icons/fi';

const TeacherExams = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  
  // Redux state - using optimized exam list
  const { teacherExamsList, loading: oldLoading, error: oldError } = useSelector((state) => state.exams);
  const { data: exams, loading, error, pagination } = teacherExamsList;
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-white text-gray-900 border-gray-300",
    button: currentTheme === "dark" ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-600 hover:bg-blue-700"
  }), [currentTheme]);

  // Fetch exams on component mount using optimized API
  useEffect(() => {
    dispatch(resetTeacherExamsList()); // Reset state first
    dispatch(fetchTeacherExamsList({ skip: 0, limit: 50 })); // Load first page
  }, [dispatch]);

  // Filter and search exams
  const filteredExams = useMemo(() => {
    if (!exams) return [];

    return exams.filter(exam => {
      // Search filter
      const matchesSearch = !searchTerm ||
        exam.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.name?.toLowerCase().includes(searchTerm.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || exam.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [exams, searchTerm, statusFilter]);

  // Calculate stats
  const stats = useMemo(() => {
    if (!exams) return { total: 0, draft: 0, active: 0, completed: 0 };
    
    return {
      total: exams.length,
      draft: exams.filter(e => e.status === 'draft').length,
      active: exams.filter(e => e.status === 'active').length,
      completed: exams.filter(e => e.status === 'completed').length
    };
  }, [exams]);

  // Handlers
  const handleCreateExam = () => {
    navigate('/teacher/create-exam');
  };

  const handleViewExam = (exam) => {
    // Check for different possible ID fields
    const examId = exam.id || exam._id || exam.exam_id;

    // Pass the exam data through navigation state
    navigate(`/teacher/exam/${examId}`, {
      state: { examData: exam }
    });
  };

  const handleEditExam = (exam) => {
    // Check for different possible ID fields
    const examId = exam.id || exam._id || exam.exam_id;
    // Pass the exam data through navigation state for pre-filling the form
    navigate(`/teacher/exam/${examId}/edit`, {
      state: { examData: exam, isEditing: true }
    });
  };

  const handleDeleteExam = async (exam) => {
    if (window.confirm(`Are you sure you want to delete "${exam.title}"? This action cannot be undone.`)) {
      try {
        const examId = exam.id || exam._id || exam.exam_id;
        await dispatch(deleteExam(examId)).unwrap();
        // Refresh the list using optimized API
        dispatch(resetTeacherExamsList());
        dispatch(fetchTeacherExamsList({ skip: 0, limit: 50 }));
      } catch (error) {
        console.error('Failed to delete exam:', error);
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      dispatch(resetTeacherExamsList()); // Reset state first
      await dispatch(fetchTeacherExamsList({ skip: 0, limit: 50 })).unwrap();
    } catch (error) {
      console.error('Failed to refresh exams:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleBulkAction = (action, selectedExams) => {
    // Implement bulk actions like bulk delete, bulk publish, etc.
  };

  return (
    <div className={`min-h-screen ${themeClasses.bg} ${themeClasses.text} transition-colors duration-300`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-violet-700 dark:text-violet-400 mb-2">
              Exam Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Create, manage, and monitor your exams
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2"
            >
              <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            <button
              onClick={handleCreateExam}
              className={`px-6 py-2 ${themeClasses.button} text-white rounded-lg transition-colors duration-200 flex items-center gap-2 font-medium`}
            >
              <FiPlus className="w-4 h-4" />
              Create New Exam
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Exams</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <FiFileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Draft</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.draft}</p>
              </div>
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <FiCalendar className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.active}</p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <FiUsers className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.completed}</p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <FiTrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6`}>
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search exams by title, description, or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent ${themeClasses.input}`}
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <FiFilter className="text-gray-400 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={`px-3 py-2 border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent ${themeClasses.input}`}
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>
        </div>

        {/* Exams Table */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden`}>
          <ExamTable
            exams={filteredExams}
            loading={loading}
            onView={handleViewExam}
            onEdit={handleEditExam}
            onDelete={handleDeleteExam}
            onBulkAction={handleBulkAction}
            showActions={true}
            userRole="teacher"
            selectable={true}
          />
        </div>

        {/* Load More Button */}
        <LoadMoreButton
          hasMore={pagination.hasMore}
          loading={loading}
          onLoadMore={() => dispatch(fetchTeacherExamsList({
            skip: pagination.skip,
            limit: pagination.limit
          }))}
          text="Load More Exams"
          theme={currentTheme}
          className="mt-6"
        />

        {/* Error Display */}
        {error && (
          <div className="mt-6 p-4 bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg">
            <p className="text-red-700 dark:text-red-300">
              Error loading exams: {typeof error === 'string' ? error : 'An unexpected error occurred'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TeacherExams;
