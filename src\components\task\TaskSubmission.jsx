import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  submitTask,
  fetchStudentTaskSubmission,
  updateTaskStatus,
  selectCurrentSubmission,
  selectSubmissionLoading,
  selectSubmissionError,
  clearSubmissionState
} from '../../store/slices/TaskSlice';
import TaskAttachments from './TaskAttachments';
import {
  FiSend,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiLoader,
  FiEdit3,
  FiSave,
  FiX,
  FiCalendar,
  FiUser
} from 'react-icons/fi';

/**
 * TaskSubmission Component
 * Handles task submission for students with text responses and file attachments
 * 
 * Props:
 * - task: Task object with details
 * - studentId: ID of the current student
 * - onSubmissionUpdate: Callback when submission is updated
 * - className: Additional CSS classes
 */
const TaskSubmission = ({
  task,
  studentId,
  onSubmissionUpdate,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const currentSubmission = useSelector(selectCurrentSubmission);
  const submissionLoading = useSelector(selectSubmissionLoading);
  const submissionError = useSelector(selectSubmissionError);

  // Local state
  const [submissionText, setSubmissionText] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load existing submission on mount
  useEffect(() => {
    if (task?.id && studentId) {
      dispatch(fetchStudentTaskSubmission({
        task_id: task.id,
        student_id: studentId
      }));
    }

    return () => {
      dispatch(clearSubmissionState());
    };
  }, [dispatch, task?.id, studentId]);

  // Update local state when submission loads
  useEffect(() => {
    if (currentSubmission) {
      setSubmissionText(currentSubmission.submission_text || '');
    }
  }, [currentSubmission]);

  // Check if task is past deadline
  const isPastDeadline = task?.deadline && new Date(task.deadline) < new Date();
  const canSubmit = !isPastDeadline || task?.accept_after_deadline;

  // Get submission status
  const getSubmissionStatus = () => {
    if (!currentSubmission) return 'not_submitted';
    return currentSubmission.status || 'submitted';
  };

  const submissionStatus = getSubmissionStatus();

  // Handle text submission
  const handleSubmit = async () => {
    if (!submissionText.trim()) {
      alert('Please enter your submission text');
      return;
    }

    try {
      const submissionData = {
        submission_text: submissionText.trim(),
        submitted_at: new Date().toISOString()
      };

      await dispatch(submitTask({
        task_id: task.id,
        submission_data: submissionData
      })).unwrap();

      setIsEditing(false);
      setShowSubmitConfirm(false);
      
      if (onSubmissionUpdate) {
        onSubmissionUpdate();
      }
    } catch (error) {
      console.error('Submission failed:', error);
    }
  };

  // Handle edit mode
  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSubmissionText(currentSubmission?.submission_text || '');
  };

  const handleSaveDraft = async () => {
    try {
      await dispatch(updateTaskStatus({
        task_id: task.id,
        status: 'in_progress',
        student_id: studentId
      })).unwrap();

      // Save as draft (you might want to add a separate API for this)
      setIsEditing(false);
    } catch (error) {
      console.error('Save draft failed:', error);
    }
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      not_submitted: {
        color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        icon: FiClock,
        text: 'Not Submitted'
      },
      in_progress: {
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
        icon: FiEdit3,
        text: 'In Progress'
      },
      submitted: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        icon: FiSend,
        text: 'Submitted'
      },
      graded: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        icon: FiCheckCircle,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    );
  };

  return (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} ${className}`}>
      {/* Header */}
      <div className={`px-6 py-4 border-b ${borderColor} ${bgSecondary}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className={`text-lg font-semibold ${textPrimary}`}>
              My Submission
            </h3>
            <div className="flex items-center gap-4 mt-2">
              <StatusBadge status={submissionStatus} />
              
              {task?.deadline && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <FiCalendar className="w-4 h-4" />
                  <span>
                    Due: {new Date(task.deadline).toLocaleDateString()} at{' '}
                    {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                  {isPastDeadline && (
                    <span className="text-red-500 font-medium">(Overdue)</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {currentSubmission?.grade && (
            <div className="text-right">
              <div className={`text-2xl font-bold ${textPrimary}`}>
                {currentSubmission.grade}
                {currentSubmission.max_grade && `/${currentSubmission.max_grade}`}
              </div>
              <div className={`text-sm ${textSecondary}`}>Grade</div>
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {submissionError && (
        <div className="px-6 py-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm">{submissionError}</span>
          </div>
        </div>
      )}

      {/* Submission Content */}
      <div className="p-6 space-y-6">
        {/* Text Submission */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className={`block text-sm font-medium ${textPrimary}`}>
              Submission Text
            </label>
            
            {!isEditing && canSubmit && submissionStatus !== 'graded' && (
              <button
                onClick={handleEdit}
                className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
              >
                <FiEdit3 className="w-4 h-4" />
                {submissionStatus === 'not_submitted' ? 'Start' : 'Edit'}
              </button>
            )}
          </div>

          {isEditing ? (
            <div className="space-y-3">
              <textarea
                value={submissionText}
                onChange={(e) => setSubmissionText(e.target.value)}
                placeholder="Enter your submission here..."
                rows={8}
                className={`w-full px-4 py-3 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} placeholder-gray-400`}
              />
              
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowSubmitConfirm(true)}
                  disabled={submissionLoading || !submissionText.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  {submissionLoading ? (
                    <FiLoader className="w-4 h-4 animate-spin" />
                  ) : (
                    <FiSend className="w-4 h-4" />
                  )}
                  Submit
                </button>
                
                <button
                  onClick={handleSaveDraft}
                  disabled={submissionLoading}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  <FiSave className="w-4 h-4" />
                  Save Draft
                </button>
                
                <button
                  onClick={handleCancelEdit}
                  className={`px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 ${textPrimary}`}
                >
                  <FiX className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className={`p-4 border ${borderColor} rounded-lg ${bgSecondary}`}>
              {submissionText ? (
                <div className={`whitespace-pre-wrap ${textPrimary}`}>
                  {submissionText}
                </div>
              ) : (
                <div className={`text-center py-8 ${textSecondary}`}>
                  <FiEdit3 className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No submission text yet</p>
                  {canSubmit && (
                    <button
                      onClick={handleEdit}
                      className="mt-2 text-blue-600 hover:text-blue-700 underline"
                    >
                      Start writing your submission
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* File Attachments */}
        <TaskAttachments
          taskId={task?.id}
          attachmentType="submission"
          canUpload={canSubmit && submissionStatus !== 'graded'}
          canDelete={canSubmit && submissionStatus !== 'graded'}
          maxFileSize={25}
          allowedTypes={['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'zip']}
        />

        {/* Teacher Feedback */}
        {currentSubmission?.feedback && (
          <div className="border-t pt-6">
            <h4 className={`text-sm font-medium ${textPrimary} mb-3 flex items-center gap-2`}>
              <FiUser className="w-4 h-4" />
              Teacher Feedback
            </h4>
            <div className={`p-4 border ${borderColor} rounded-lg ${bgSecondary}`}>
              <div className={`whitespace-pre-wrap ${textPrimary}`}>
                {currentSubmission.feedback}
              </div>
              {currentSubmission.feedback_date && (
                <div className={`text-xs ${textSecondary} mt-2`}>
                  {new Date(currentSubmission.feedback_date).toLocaleString()}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${bgPrimary} rounded-lg p-6 max-w-md w-full mx-4 border ${borderColor}`}>
            <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>
              Confirm Submission
            </h3>
            <p className={`${textSecondary} mb-6`}>
              Are you sure you want to submit this task? You may not be able to edit it after submission.
            </p>
            <div className="flex items-center gap-3">
              <button
                onClick={handleSubmit}
                disabled={submissionLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {submissionLoading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiSend className="w-4 h-4" />
                )}
                Submit
              </button>
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className={`flex-1 px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textPrimary}`}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskSubmission;
