import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  uploadTaskAttachment,
  fetchTaskAttachments,
  deleteTaskAttachment,
  selectAttachments,
  selectAttachmentLoading,
  selectAttachmentError,
  clearAttachmentState
} from '../../store/slices/TaskSlice';
import {
  FiUpload,
  FiFile,
  FiDownload,
  FiTrash2,
  FiX,
  FiPaperclip,
  FiLoader,
  FiAlertCircle,
  FiCheck,
  FiImage,
  FiFileText,
  FiVideo
} from 'react-icons/fi';

/**
 * TaskAttachments Component
 * Handles file uploads, downloads, and attachment management for tasks
 * 
 * Props:
 * - taskId: ID of the task
 * - attachmentType: Type of attachment ('task' for task files, 'submission' for student submissions)
 * - canUpload: Whether user can upload files (default: true)
 * - canDelete: Whether user can delete files (default: true)
 * - maxFileSize: Maximum file size in MB (default: 10)
 * - allowedTypes: Array of allowed file types (default: all)
 * - className: Additional CSS classes
 */
const TaskAttachments = ({
  taskId,
  attachmentType = 'task',
  canUpload = true,
  canDelete = true,
  maxFileSize = 10,
  allowedTypes = [],
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const fileInputRef = useRef(null);

  // Redux state
  const attachments = useSelector(selectAttachments);
  const loading = useSelector(selectAttachmentLoading);
  const error = useSelector(selectAttachmentError);

  // Local state
  const [dragOver, setDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load attachments on mount
  useEffect(() => {
    if (taskId) {
      dispatch(fetchTaskAttachments({ task_id: taskId, attachment_type: attachmentType }));
    }
    
    return () => {
      dispatch(clearAttachmentState());
    };
  }, [dispatch, taskId, attachmentType]);

  // File validation
  const validateFile = (file) => {
    const errors = [];
    
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      errors.push(`File size must be less than ${maxFileSize}MB`);
    }
    
    // Check file type
    if (allowedTypes.length > 0) {
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        errors.push(`File type .${fileExtension} is not allowed`);
      }
    }
    
    return errors;
  };

  // Handle file selection
  const handleFileSelect = (files) => {
    Array.from(files).forEach(file => {
      const errors = validateFile(file);
      if (errors.length > 0) {
        alert(`Error with file "${file.name}":\n${errors.join('\n')}`);
        return;
      }
      
      uploadFile(file);
    });
  };

  // Upload file
  const uploadFile = async (file) => {
    try {
      setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
      
      await dispatch(uploadTaskAttachment({
        task_id: taskId,
        file,
        attachment_type: attachmentType
      })).unwrap();
      
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[file.name];
        return newProgress;
      });
      
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[file.name];
        return newProgress;
      });
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files);
      e.target.value = ''; // Reset input
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // Delete attachment
  const handleDelete = async (attachmentId) => {
    if (window.confirm('Are you sure you want to delete this attachment?')) {
      try {
        await dispatch(deleteTaskAttachment({
          task_id: taskId,
          attachment_id: attachmentId
        })).unwrap();
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  // Download attachment
  const handleDownload = (attachment) => {
    if (attachment.download_url) {
      const link = document.createElement('a');
      link.href = attachment.download_url;
      link.download = attachment.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Get file icon
  const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension)) {
      return <FiImage className="w-5 h-5 text-blue-500" />;
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(extension)) {
      return <FiVideo className="w-5 h-5 text-purple-500" />;
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
      return <FiFileText className="w-5 h-5 text-red-500" />;
    } else {
      return <FiFile className="w-5 h-5 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} ${className}`}>
      {/* Header */}
      <div className={`px-4 py-3 border-b ${borderColor} ${bgSecondary}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FiPaperclip className={`w-5 h-5 ${textPrimary}`} />
            <h3 className={`font-medium ${textPrimary}`}>
              {attachmentType === 'submission' ? 'Submission Files' : 'Task Attachments'}
            </h3>
            <span className={`text-sm ${textSecondary}`}>
              ({attachments.length})
            </span>
          </div>
          
          {canUpload && (
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={loading}
              className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2 text-sm"
            >
              {loading ? (
                <FiLoader className="w-4 h-4 animate-spin" />
              ) : (
                <FiUpload className="w-4 h-4" />
              )}
              Upload
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="px-4 py-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Upload Area */}
      {canUpload && (
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`p-4 border-b ${borderColor} transition-colors ${
            dragOver ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600' : ''
          }`}
        >
          <div className={`text-center ${textSecondary}`}>
            <FiUpload className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              Drag and drop files here or{' '}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                browse
              </button>
            </p>
            <p className="text-xs mt-1">
              Max file size: {maxFileSize}MB
              {allowedTypes.length > 0 && (
                <span> • Allowed: {allowedTypes.join(', ')}</span>
              )}
            </p>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileInputChange}
            className="hidden"
            accept={allowedTypes.length > 0 ? allowedTypes.map(type => `.${type}`).join(',') : undefined}
          />
        </div>
      )}

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          {Object.entries(uploadProgress).map(([filename, progress]) => (
            <div key={filename} className="mb-2 last:mb-0">
              <div className="flex items-center justify-between text-sm">
                <span className={textSecondary}>{filename}</span>
                <span className={textSecondary}>Uploading...</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '50%' }}></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Attachments List */}
      <div className="p-4">
        {loading && attachments.length === 0 ? (
          <div className="text-center py-8">
            <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
            <p className={`text-sm ${textSecondary}`}>Loading attachments...</p>
          </div>
        ) : attachments.length === 0 ? (
          <div className="text-center py-8">
            <FiFile className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
            <p className={`text-sm ${textSecondary}`}>No attachments yet</p>
          </div>
        ) : (
          <div className="space-y-2">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className={`flex items-center justify-between p-3 rounded-lg border ${borderColor} hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors`}
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  {getFileIcon(attachment.filename)}
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium ${textPrimary} truncate`}>
                      {attachment.filename}
                    </p>
                    <p className={`text-xs ${textSecondary}`}>
                      {formatFileSize(attachment.file_size)} • {new Date(attachment.uploaded_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleDownload(attachment)}
                    className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-blue-600`}
                    title="Download"
                  >
                    <FiDownload className="w-4 h-4" />
                  </button>
                  
                  {canDelete && (
                    <button
                      onClick={() => handleDelete(attachment.id)}
                      className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-red-600`}
                      title="Delete"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskAttachments;
