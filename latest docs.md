# Comprehensive Exam Details API - Frontend Developer Guide

## Overview
This guide covers the new comprehensive exam details API that provides complete exam information including assignment details, available students, and comprehensive statistics. This API is designed for exam management dashboards and analytics interfaces.

## Authentication
All endpoints require teacher authentication:
```javascript
headers: {
  'Authorization': `Bear<PERSON> ${teacherToken}`,
  'Content-Type': 'application/json'
}
```

## API Endpoints

### 1. Get Comprehensive Exam Details
**Endpoint:** `GET /api/exams/{exam_id}/comprehensive`

**Purpose:** Get complete exam information including basic details, assignments, available students, and statistics.

**Response Schema:**
```typescript
interface ExamComprehensiveDetails {
  // Basic exam information
  id: string;
  title: string;
  description?: string;
  total_marks: number;
  total_duration: number;  // in minutes
  start_time?: string;     // ISO datetime
  
  // Questions
  questions: Question[];
  
  // Assignment information
  assignment_details: {
    exam_id: string;
    assigned_student_ids: string[];
  };
  
  // Available students for assignment
  available_students: AvailableStudent[];
  
  // Comprehensive statistics
  statistics: ExamStatistics;
}

interface AvailableStudent {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_assigned: boolean;
}

interface ExamStatistics {
  total_assigned: number;
  total_attempts: number;
  completed_attempts: number;
  completion_rate: number;  // percentage
  marks_statistics: ExamMarksStatistics;
  top_performers: TopPerformer[];
}

interface ExamMarksStatistics {
  total_submissions: number;
  highest_marks: number;
  lowest_marks: number;
  average_marks: number;
  standard_deviation: number;
}

interface TopPerformer {
  student_id: string;
  student_name: string;
  student_email: string;
  total_score: number;
  completed_at: string;  // ISO datetime
}
```

**Example Usage:**
```javascript
const getComprehensiveExamDetails = async (examId) => {
  const response = await fetch(`/api/exams/${examId}/comprehensive`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    throw new Error('Failed to fetch exam details');
  }
  
  return response.json();
};

// Usage in React component
const ExamDashboard = ({ examId }) => {
  const [examDetails, setExamDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const details = await getComprehensiveExamDetails(examId);
        setExamDetails(details);
      } catch (error) {
        console.error('Error fetching exam details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, [examId]);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="exam-dashboard">
      <h1>{examDetails.title}</h1>
      
      {/* Basic Info */}
      <div className="exam-info">
        <p>Duration: {examDetails.total_duration} minutes</p>
        <p>Total Marks: {examDetails.total_marks}</p>
        <p>Questions: {examDetails.questions.length}</p>
      </div>

      {/* Statistics */}
      <div className="exam-stats">
        <h3>Statistics</h3>
        <p>Assigned: {examDetails.statistics.total_assigned}</p>
        <p>Completed: {examDetails.statistics.completed_attempts}</p>
        <p>Completion Rate: {examDetails.statistics.completion_rate}%</p>
        <p>Average Score: {examDetails.statistics.marks_statistics.average_marks}</p>
      </div>

      {/* Assignment Management */}
      <div className="assignment-section">
        <h3>Assignment Management</h3>
        <p>Currently Assigned: {examDetails.assignment_details.assigned_student_ids.length}</p>
        <p>Available Students: {examDetails.available_students.length}</p>
      </div>
    </div>
  );
};
```

### 2. Get Available Students for Assignment
**Endpoint:** `GET /api/exams/{exam_id}/available-students`

**Purpose:** Get all students from teacher's classrooms with their assignment status for this exam.

**Response:** `AvailableStudent[]`

**Example Usage:**
```javascript
const getAvailableStudents = async (examId) => {
  const response = await fetch(`/api/exams/${examId}/available-students`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Usage for assignment interface
const StudentAssignmentModal = ({ examId }) => {
  const [availableStudents, setAvailableStudents] = useState([]);

  useEffect(() => {
    const fetchStudents = async () => {
      const students = await getAvailableStudents(examId);
      setAvailableStudents(students);
    };
    fetchStudents();
  }, [examId]);

  const assignedStudents = availableStudents.filter(s => s.is_assigned);
  const unassignedStudents = availableStudents.filter(s => !s.is_assigned);

  return (
    <div className="assignment-modal">
      <h3>Manage Assignments</h3>
      
      <div className="assigned-section">
        <h4>Currently Assigned ({assignedStudents.length})</h4>
        {assignedStudents.map(student => (
          <div key={student.id} className="student-item assigned">
            <span>{student.username} ({student.email})</span>
            <button onClick={() => unassignStudent(student.id)}>
              Remove
            </button>
          </div>
        ))}
      </div>

      <div className="unassigned-section">
        <h4>Available for Assignment ({unassignedStudents.length})</h4>
        {unassignedStudents.map(student => (
          <div key={student.id} className="student-item unassigned">
            <span>{student.username} ({student.email})</span>
            <button onClick={() => assignStudent(student.id)}>
              Assign
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 3. Get Exam Statistics Only
**Endpoint:** `GET /api/exams/{exam_id}/statistics`

**Purpose:** Get detailed exam statistics for analytics dashboards.

**Response:** `ExamStatistics`

**Example Usage:**
```javascript
const getExamStatistics = async (examId) => {
  const response = await fetch(`/api/exams/${examId}/statistics`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Usage for analytics dashboard
const ExamAnalytics = ({ examId }) => {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      const statistics = await getExamStatistics(examId);
      setStats(statistics);
    };
    fetchStats();
  }, [examId]);

  if (!stats) return <div>Loading statistics...</div>;

  return (
    <div className="exam-analytics">
      <h2>Exam Analytics</h2>
      
      {/* Overview Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Completion Rate</h3>
          <div className="stat-value">{stats.completion_rate}%</div>
          <div className="stat-detail">
            {stats.completed_attempts} of {stats.total_assigned} students
          </div>
        </div>

        <div className="stat-card">
          <h3>Average Score</h3>
          <div className="stat-value">{stats.marks_statistics.average_marks}</div>
          <div className="stat-detail">
            Range: {stats.marks_statistics.lowest_marks} - {stats.marks_statistics.highest_marks}
          </div>
        </div>

        <div className="stat-card">
          <h3>Standard Deviation</h3>
          <div className="stat-value">{stats.marks_statistics.standard_deviation}</div>
          <div className="stat-detail">Score distribution</div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="top-performers">
        <h3>Top Performers</h3>
        {stats.top_performers.map((performer, index) => (
          <div key={performer.student_id} className="performer-item">
            <span className="rank">#{index + 1}</span>
            <span className="name">{performer.student_name}</span>
            <span className="score">{performer.total_score}</span>
            <span className="date">
              {new Date(performer.completed_at).toLocaleDateString()}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## UI Component Examples

### Complete Exam Management Dashboard
```jsx
const ExamManagementDashboard = ({ examId }) => {
  const [examDetails, setExamDetails] = useState(null);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);

  const refreshExamDetails = async () => {
    const details = await getComprehensiveExamDetails(examId);
    setExamDetails(details);
  };

  useEffect(() => {
    refreshExamDetails();
  }, [examId]);

  if (!examDetails) return <div>Loading...</div>;

  return (
    <div className="exam-management-dashboard">
      {/* Header */}
      <div className="exam-header">
        <h1>{examDetails.title}</h1>
        <div className="exam-meta">
          <span>{examDetails.total_duration} min</span>
          <span>{examDetails.total_marks} marks</span>
          <span>{examDetails.questions.length} questions</span>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="quick-stats">
        <div className="stat">
          <label>Assigned</label>
          <value>{examDetails.statistics.total_assigned}</value>
        </div>
        <div className="stat">
          <label>Completed</label>
          <value>{examDetails.statistics.completed_attempts}</value>
        </div>
        <div className="stat">
          <label>Completion Rate</label>
          <value>{examDetails.statistics.completion_rate}%</value>
        </div>
        <div className="stat">
          <label>Average Score</label>
          <value>{examDetails.statistics.marks_statistics.average_marks}</value>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="actions">
        <button onClick={() => setShowAssignmentModal(true)}>
          Manage Assignments
        </button>
        <button onClick={() => window.open(`/exams/${examId}/analytics`)}>
          View Analytics
        </button>
      </div>

      {/* Assignment Modal */}
      {showAssignmentModal && (
        <StudentAssignmentModal 
          examId={examId}
          onClose={() => setShowAssignmentModal(false)}
          onUpdate={refreshExamDetails}
        />
      )}
    </div>
  );
};
```

## Error Handling

```javascript
const handleApiError = (error, response) => {
  switch (response?.status) {
    case 403:
      return "You don't have permission to view this exam";
    case 404:
      return "Exam not found";
    case 500:
      return "Server error. Please try again later";
    default:
      return "An unexpected error occurred";
  }
};
```

## Best Practices

1. **Caching**: Cache exam details and refresh only when needed
2. **Loading States**: Always show loading indicators for better UX
3. **Error Handling**: Provide meaningful error messages
4. **Real-time Updates**: Consider WebSocket connections for live statistics
5. **Performance**: Use pagination for large student lists
6. **Accessibility**: Ensure proper ARIA labels for statistics

This comprehensive API provides everything needed to build sophisticated exam management and analytics interfaces!
