import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTaskById,
  selectCurrentTask,
  selectTasksLoading,
  selectTasksError,
  clearTaskState
} from '../../store/slices/TaskSlice';
import { selectCurrentUser } from '../../store/slices/userSlice';
import TaskAttachments from '../../components/task/TaskAttachments';
import TaskSubmission from '../../components/task/TaskSubmission';
import {
  FiArrowLeft,
  FiCalendar,
  FiClock,
  FiBook,
  FiUser,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle,
  FiSend,
  FiEdit3,
  FiStar
} from 'react-icons/fi';

/**
 * StudentTaskDetail Page
 * Detailed view of a task for students with submission interface
 */
const StudentTaskDetail = () => {
  const { taskId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const task = useSelector(selectCurrentTask);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);
  const currentUser = useSelector(selectCurrentUser);

  // Local state
  const [activeTab, setActiveTab] = useState('details');

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load task on mount
  useEffect(() => {
    if (taskId) {
      dispatch(fetchTaskById(taskId));
    }

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch, taskId]);

  // Check if task is overdue
  const isOverdue = task?.deadline && new Date(task.deadline) < new Date();
  const canSubmit = !isOverdue || task?.accept_after_deadline;

  // Status badge component
  const StatusBadge = ({ status, isOverdue: overdue }) => {
    const statusConfig = {
      not_submitted: {
        color: overdue 
          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        icon: overdue ? FiAlertCircle : FiClock,
        text: overdue ? 'Overdue' : 'Not Started'
      },
      in_progress: {
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
        icon: FiEdit3,
        text: 'In Progress'
      },
      submitted: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        icon: FiSend,
        text: 'Submitted'
      },
      graded: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        icon: FiCheckCircle,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4" />
        {config.text}
      </span>
    );
  };

  if (loading && !task) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
          <p className={`text-sm ${textSecondary}`}>Loading task details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <FiAlertCircle className={`w-8 h-8 mx-auto mb-2 text-red-500`} />
          <p className={`text-sm ${textSecondary}`}>{error}</p>
          <button
            onClick={() => navigate('/student/tasks')}
            className="mt-4 text-blue-600 hover:text-blue-700 underline"
          >
            Back to My Tasks
          </button>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-center py-12">
          <p className={`text-sm ${textSecondary}`}>Task not found</p>
          <button
            onClick={() => navigate('/student/tasks')}
            className="mt-4 text-blue-600 hover:text-blue-700 underline"
          >
            Back to My Tasks
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/student/tasks')}
          className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${textSecondary}`}
        >
          <FiArrowLeft className="w-5 h-5" />
        </button>
        
        <div className="flex-1">
          <h1 className={`text-2xl font-bold ${textPrimary}`}>{task.name}</h1>
          <div className="flex items-center gap-4 mt-2">
            <StatusBadge status={task.submission_status || 'not_submitted'} isOverdue={isOverdue} />
            
            {task.subject && (
              <div className="flex items-center gap-1">
                <FiBook className={`w-4 h-4 ${textSecondary}`} />
                <span className={`text-sm ${textSecondary}`}>{task.subject}</span>
              </div>
            )}
            
            {task.teacher_name && (
              <div className="flex items-center gap-1">
                <FiUser className={`w-4 h-4 ${textSecondary}`} />
                <span className={`text-sm ${textSecondary}`}>{task.teacher_name}</span>
              </div>
            )}
          </div>
        </div>

        {task.grade !== null && task.grade !== undefined && (
          <div className="text-right">
            <div className={`text-2xl font-bold ${textPrimary}`}>
              {task.grade}
              {task.max_grade && `/${task.max_grade}`}
            </div>
            <div className={`text-sm ${textSecondary} flex items-center gap-1`}>
              <FiStar className="w-4 h-4" />
              {task.max_grade && `${Math.round((task.grade / task.max_grade) * 100)}%`}
            </div>
          </div>
        )}
      </div>

      {/* Deadline Warning */}
      {task.deadline && (
        <div className={`mb-6 p-4 rounded-lg border ${
          isOverdue 
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : new Date(task.deadline) - new Date() < 24 * 60 * 60 * 1000
              ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
              : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
        }`}>
          <div className="flex items-center gap-2">
            <FiCalendar className={`w-4 h-4 ${
              isOverdue ? 'text-red-600' : 'text-blue-600'
            }`} />
            <span className={`text-sm font-medium ${
              isOverdue ? 'text-red-600' : 'text-blue-600'
            }`}>
              {isOverdue ? 'This task is overdue' : 'Deadline'}:
            </span>
            <span className={`text-sm ${
              isOverdue ? 'text-red-600' : 'text-blue-600'
            }`}>
              {new Date(task.deadline).toLocaleDateString()} at{' '}
              {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
            {task.accept_after_deadline && isOverdue && (
              <span className="text-sm text-green-600 ml-2">
                (Late submissions accepted)
              </span>
            )}
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor} mb-6`}>
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'details', label: 'Task Details', icon: FiBook },
            { id: 'submission', label: 'My Submission', icon: FiSend }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : `border-transparent ${textSecondary} hover:text-gray-700 dark:hover:text-gray-300`
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'details' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Task Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Description</h3>
              
              {task.description ? (
                <div className={`${textPrimary} whitespace-pre-wrap leading-relaxed`}>
                  {task.description}
                </div>
              ) : (
                <div className={`text-center py-8 ${textSecondary}`}>
                  <FiBook className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No description provided for this task</p>
                </div>
              )}
            </div>

            {/* Task Files */}
            <div>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Files</h3>
              <TaskAttachments
                taskId={task.id}
                attachmentType="task"
                canUpload={false}
                canDelete={false}
              />
            </div>
          </div>

          {/* Task Info Sidebar */}
          <div className="space-y-6">
            {/* Basic Information */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Information</h3>
              
              <div className="space-y-4">
                <div>
                  <p className={`text-sm ${textSecondary} mb-1`}>Subject</p>
                  <p className={`font-medium ${textPrimary}`}>{task.subject || 'No Subject'}</p>
                </div>
                
                <div>
                  <p className={`text-sm ${textSecondary} mb-1`}>Teacher</p>
                  <p className={`font-medium ${textPrimary}`}>{task.teacher_name || 'Unknown'}</p>
                </div>
                
                <div>
                  <p className={`text-sm ${textSecondary} mb-1`}>Created</p>
                  <p className={`font-medium ${textPrimary}`}>
                    {new Date(task.created_at).toLocaleDateString()}
                  </p>
                </div>
                
                {task.deadline && (
                  <div>
                    <p className={`text-sm ${textSecondary} mb-1`}>Deadline</p>
                    <p className={`font-medium ${isOverdue ? 'text-red-600' : textPrimary}`}>
                      {new Date(task.deadline).toLocaleDateString()}
                      <br />
                      <span className="text-sm">
                        {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </p>
                  </div>
                )}
                
                {task.max_grade && (
                  <div>
                    <p className={`text-sm ${textSecondary} mb-1`}>Max Grade</p>
                    <p className={`font-medium ${textPrimary}`}>{task.max_grade} points</p>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
              <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>Quick Actions</h3>
              
              <div className="space-y-3">
                <button
                  onClick={() => setActiveTab('submission')}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <FiSend className="w-4 h-4" />
                  {task.submission_status === 'not_submitted' ? 'Start Submission' : 'View Submission'}
                </button>
                
                {canSubmit && task.submission_status !== 'graded' && (
                  <button
                    onClick={() => setActiveTab('submission')}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <FiEdit3 className="w-4 h-4" />
                    {task.submission_status === 'not_submitted' ? 'Submit Work' : 'Edit Submission'}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'submission' && (
        <TaskSubmission
          task={task}
          studentId={currentUser?.id}
          onSubmissionUpdate={() => {
            // Refresh task data
            dispatch(fetchTaskById(taskId));
          }}
        />
      )}
    </div>
  );
};

export default StudentTaskDetail;
