// EduFair UI Component Library
// Comprehensive export of all reusable UI components

// Card Components
export * from './cards';

// Form Components  
export * from './forms';

// Button Components
export * from './buttons';

// Modal Components
export * from './modals';

// Table Components
export * from './tables';

// Navigation Components
export * from './navigation';

// Layout Components
export * from './layout';

// Legacy Components (for backward compatibility)
export { default as DataTable } from './DataTable';
export * from './FormComponents';

// Newly organized components
export { default as Toast } from './Toast';
export { default as LoadingSpinner } from './LoadingSpinner';
export { default as ErrorMessage } from './ErrorMessage';
export { default as EmptyState } from './EmptyState';
export { default as SkeletonLoader } from './SkeletonLoader';
export { default as ErrorStates } from './ErrorStates';
export { default as ErrorBoundary } from './ErrorBoundary';
export { default as AutoSaveIndicator } from './AutoSaveIndicator';
export { default as SearchFilterCard } from './SearchFilterCard';
export { default as TabNavigation } from './TabNavigation';
export { default as Transition } from './Transition';
export { default as DropdownEditMenu } from './DropdownEditMenu';
export { default as DropdownFilter } from './DropdownFilter';
export { default as DropdownHelp } from './DropdownHelp';
export { default as DropdownNotifications } from './DropdownNotifications';
export { default as DropdownProfile } from './DropdownProfile';
export { default as ModalSearch } from './ModalSearch';

// Component Categories for organized imports
export const Cards = {
  UserCard: require('./cards').UserCard,
  ClassroomCard: require('./cards').ClassroomCard,
  DashboardCard: require('./cards').DashboardCard,
  StatsCard: require('./cards').StatsCard,
  ChartCard: require('./cards').ChartCard,
  InfoCard: require('./cards').InfoCard,
  AnnouncementCard: require('./cards').AnnouncementCard,
  NotificationCard: require('./cards').NotificationCard
};

export const Forms = {
  FormModal: require('./forms').FormModal,
  ConfirmModal: require('./forms').ConfirmModal,
  FormButton: require('./forms').FormButton,
  SubmitButton: require('./forms').SubmitButton,
  CancelButton: require('./forms').CancelButton,
  SaveButton: require('./forms').SaveButton,
  DeleteButton: require('./forms').DeleteButton,
  AddButton: require('./forms').AddButton,
  ButtonGroup: require('./forms').ButtonGroup,
  useFormValidation: require('./forms').useFormValidation,
  validationRules: require('./forms').validationRules,
  ValidationMessage: require('./forms').ValidationMessage,
  FieldValidationIndicator: require('./forms').FieldValidationIndicator,
  FormSummary: require('./forms').FormSummary
};

export const Buttons = {
  Button: require('./buttons').Button,
  IconButton: require('./buttons').IconButton,
  LoadingButton: require('./buttons').LoadingButton,
  BadgeButton: require('./buttons').BadgeButton,
  ToggleButton: require('./buttons').ToggleButton,
  ViewButton: require('./buttons').ViewButton,
  EditButton: require('./buttons').EditButton,
  DeleteButton: require('./buttons').DeleteButton,
  DownloadButton: require('./buttons').DownloadButton,
  ShareButton: require('./buttons').ShareButton,
  CopyButton: require('./buttons').CopyButton,
  ExternalLinkButton: require('./buttons').ExternalLinkButton,
  RefreshButton: require('./buttons').RefreshButton,
  SettingsButton: require('./buttons').SettingsButton,
  MoreActionsButton: require('./buttons').MoreActionsButton,
  ActionButtonGroup: require('./buttons').ActionButtonGroup,
  QuickActionBar: require('./buttons').QuickActionBar
};

export const Modals = {
  BaseModal: require('./modals').BaseModal,
  ModalHeader: require('./modals').ModalHeader,
  ModalBody: require('./modals').ModalBody,
  ModalFooter: require('./modals').ModalFooter,
  InfoModal: require('./modals').InfoModal,
  AlertModal: require('./modals').AlertModal,
  ConfirmationModal: require('./modals').ConfirmationModal,
  NotificationModal: require('./modals').NotificationModal,
  SuccessModal: require('./modals').SuccessModal,
  ErrorModal: require('./modals').ErrorModal,
  ContentModal: require('./modals').ContentModal,
  ImageModal: require('./modals').ImageModal,
  VideoModal: require('./modals').VideoModal,
  DocumentModal: require('./modals').DocumentModal,
  PreviewModal: require('./modals').PreviewModal
};

export const Tables = {
  DataTable: require('./tables').DataTable,
  UserTable: require('./tables').UserTable,
  CompactUserTable: require('./tables').CompactUserTable,
  ClassroomTable: require('./tables').ClassroomTable,
  CompactClassroomTable: require('./tables').CompactClassroomTable,
  ExamTable: require('./tables').ExamTable
};

export const Navigation = {
  Breadcrumbs: require('./navigation').Breadcrumbs,
  CompactBreadcrumbs: require('./navigation').CompactBreadcrumbs,
  createBreadcrumbItem: require('./navigation').createBreadcrumbItem,
  breadcrumbPatterns: require('./navigation').breadcrumbPatterns,
  useBreadcrumbs: require('./navigation').useBreadcrumbs,
  Tabs: require('./navigation').Tabs,
  SimpleTabs: require('./navigation').SimpleTabs,
  VerticalTabs: require('./navigation').VerticalTabs,
  PillTabs: require('./navigation').PillTabs,
  useTabs: require('./navigation').useTabs,
  Pagination: require('./navigation').Pagination,
  SimplePagination: require('./navigation').SimplePagination,
  CompactPagination: require('./navigation').CompactPagination,
  usePagination: require('./navigation').usePagination,
  ActionMenu: require('./navigation').ActionMenu,
  createActionItems: require('./navigation').createActionItems
};

export const Layout = {
  PageHeader: require('./layout').PageHeader,
  SimplePageHeader: require('./layout').SimplePageHeader,
  DashboardPageHeader: require('./layout').DashboardPageHeader,
  FormPageHeader: require('./layout').FormPageHeader,
  PageContainer: require('./layout').PageContainer,
  NarrowPageContainer: require('./layout').NarrowPageContainer,
  WidePageContainer: require('./layout').WidePageContainer,
  CompactPageContainer: require('./layout').CompactPageContainer,
  FluidPageContainer: require('./layout').FluidPageContainer,
  SectionDivider: require('./layout').SectionDivider,
  LineDivider: require('./layout').LineDivider,
  SpaceDivider: require('./layout').SpaceDivider,
  GradientDivider: require('./layout').GradientDivider,
  DashedDivider: require('./layout').DashedDivider,
  LabeledDivider: require('./layout').LabeledDivider,
  VerticalDivider: require('./layout').VerticalDivider,
  ContentWrapper: require('./layout').ContentWrapper,
  Card: require('./layout').Card,
  Panel: require('./layout').Panel,
  Section: require('./layout').Section,
  MinimalWrapper: require('./layout').MinimalWrapper,
  GridWrapper: require('./layout').GridWrapper,
  FlexWrapper: require('./layout').FlexWrapper,
  Stack: require('./layout').Stack,
  Inline: require('./layout').Inline
};
