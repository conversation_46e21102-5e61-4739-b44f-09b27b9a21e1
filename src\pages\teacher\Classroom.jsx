import React, { useEffect, useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchAllOwnClasses } from "../../store/slices/ClassroomSlice";
import CreateClassRoomForm from "./CreateClassroomForm";
import SearchFilterCard from "../../components/ui/SearchFilterCard";
import SkeletonLoader from "../../components/ui/SkeletonLoader";
import { useStableCallback } from "../../utils/helpers/performance";
import logger from "../../utils/helpers/logger";
import { handleApiError } from "../../utils/helpers/errorHandler";

import {
  FiUsers,
  FiCheckCircle,
  FiPlus,
  FiRefreshCw,
  FiEye,
  FiEdit,
  FiMoreVertical,
  FiCalendar,
  FiBookOpen
} from "react-icons/fi";

function Classroom() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const { classrooms, loading, error } = useSelector((state) => state.classroom);

  useEffect(() => {
    const loadClassrooms = async () => {
      try {
        await dispatch(fetchAllOwnClasses()).unwrap();
        logger.info('Classrooms loaded successfully', null, 'Classroom');
      } catch (error) {
        const errorData = handleApiError(error, 'Classroom');
        logger.error('Failed to load classrooms', errorData, 'Classroom');
      }
    };

    loadClassrooms();
  }, [dispatch]);

  const handleClick = useStableCallback((classroomId) => {
    logger.userAction('classroom_click', { classroomId }, 'Classroom');
    navigate(`/teacher/classroom/${classroomId}`);
  }, [navigate]);

  const handleRefresh = useStableCallback(async () => {
    setRefreshing(true);
    try {
      await dispatch(fetchAllOwnClasses()).unwrap();
      logger.info('Classrooms refreshed successfully', null, 'Classroom');
    } catch (error) {
      const errorData = handleApiError(error, 'Classroom');
      logger.error('Failed to refresh classrooms', errorData, 'Classroom');
    } finally {
      setRefreshing(false);
    }
  }, [dispatch]);

  const handleSearchChange = useStableCallback((value) => {
    setSearchTerm(value);
  }, []);

  const handleSearchSubmit = useStableCallback((value) => {
    setSearchTerm(value);
  }, []);

  const handleSearchClear = useStableCallback(() => {
    setSearchTerm('');
  }, []);

  // Filter and search classrooms
  const filteredClassrooms = useMemo(() => {
    if (!classrooms) return [];

    return classrooms.filter(classroom => {
      const matchesSearch = !searchTerm ||
        classroom.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        classroom.description?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = filterStatus === 'all' ||
        (filterStatus === 'active' && classroom.is_active) ||
        (filterStatus === 'inactive' && !classroom.is_active);

      return matchesSearch && matchesStatus;
    });
  }, [classrooms, searchTerm, filterStatus]);

  // Calculate stats
  const stats = useMemo(() => {
    if (!classrooms) return { total: 0, active: 0, totalStudents: 0 };

    const total = classrooms.length;
    const active = classrooms.filter(c => c.is_active).length;
    const totalStudents = classrooms.reduce((sum, c) => sum + (c.students?.length || 0), 0);

    return { total, active, totalStudents };
  }, [classrooms]);

  if (loading && !classrooms) {
    return (
      <div className="space-y-6">
        <SkeletonLoader type="page" showStats={true} showCards={true} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Classrooms
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your teaching classrooms and students
          </p>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn btn-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-600 dark:text-gray-300"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="btn btn-sm bg-violet-500 hover:bg-violet-600 text-white"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Create Classroom
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Classrooms</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
              <FiBookOpen className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Classrooms</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.active}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
              <FiCheckCircle className="text-green-600 dark:text-green-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.totalStudents}</p>
            </div>
            <div className="w-12 h-12 bg-violet-100 dark:bg-violet-900/30 rounded-xl flex items-center justify-center">
              <FiUsers className="text-violet-600 dark:text-violet-400" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        onSearchClear={handleSearchClear}
        searchPlaceholder="Search classrooms by name or description..."
        filters={[
          {
            label: 'Status',
            value: filterStatus,
            onChange: setFilterStatus,
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ]
          }
        ]}
        resultsCount={filteredClassrooms.length}
        resultsType="classrooms"
        showViewToggle={false}
      />

      {/* Classroom Cards */}
      {error ? (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
          <div className="text-red-600 dark:text-red-400 mb-2">Failed to load classrooms</div>
          <p className="text-red-500 dark:text-red-400 text-sm mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="btn bg-red-600 hover:bg-red-700 text-white"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      ) : filteredClassrooms.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClassrooms.map((classroom) => (
            <div
              key={classroom.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-all duration-200 p-6 border border-transparent hover:border-violet-200 dark:hover:border-violet-700 cursor-pointer"
              onClick={() => handleClick(classroom.id)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                    {classroom.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                    {classroom.description || "No description available"}
                  </p>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle more actions
                  }}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FiMoreVertical className="w-4 h-4" />
                </button>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                <div className="flex items-center">
                  <FiUsers className="w-4 h-4 mr-1" />
                  <span>{classroom.student_count || 0} students</span>
                </div>
                <div className="flex items-center">
                  <FiCheckCircle className="w-4 h-4 mr-1" />
                  <span>{classroom.class_request_count || 0} requests</span>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClick(classroom.id);
                  }}
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                >
                  <FiEye className="w-4 h-4 mr-1 inline" />
                  View
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiBookOpen className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            {searchTerm ? 'No classrooms match your search' : 'No classrooms found'}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchTerm
              ? 'Try adjusting your search criteria.'
              : 'Create your first classroom to start teaching.'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={() => setShowForm(true)}
              className="btn bg-violet-500 hover:bg-violet-600 text-white"
            >
              <FiPlus className="w-4 h-4 mr-2" />
              Create Classroom
            </button>
          )}
        </div>
      )}

      {/* Modal Form */}
      {showForm && <CreateClassRoomForm onClose={() => setShowForm(false)} />}
    </div>
  );
}

export default Classroom;
