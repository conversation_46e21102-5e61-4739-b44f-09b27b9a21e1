import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchAllTasksWithFilters,
  fetchTaskSubmissions,
  selectTasks,
  selectTasksLoading,
  selectTasksError,
  selectSubmissions,
  clearTaskState
} from '../../store/slices/TaskSlice';
import CreateTask from './CreateTask';
import {
  FiPlus,
  FiSearch,
  FiFilter,
  FiCalendar,
  FiUsers,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiEye,
  FiEdit3,
  FiTrash2,
  FiLoader,
  FiBook
} from 'react-icons/fi';

/**
 * TeacherTasks Page
 * Comprehensive task management dashboard for teachers
 */
const TeacherTasks = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const tasks = useSelector(selectTasks);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);

  // Local state
  const [showCreateTask, setShowCreateTask] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load tasks on mount
  useEffect(() => {
    const filters = {
      status: statusFilter !== 'all' ? statusFilter : undefined,
      subject: subjectFilter !== 'all' ? subjectFilter : undefined,
      search: searchTerm || undefined,
      sort_by: sortBy,
      sort_order: sortOrder
    };

    dispatch(fetchAllTasksWithFilters(filters));

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch, statusFilter, subjectFilter, searchTerm, sortBy, sortOrder]);

  // Filter and sort tasks
  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [tasks, searchTerm]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalTasks = tasks.length;
    const activeTasks = tasks.filter(t => t.status === 'active').length;
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const pendingGrading = tasks.filter(t => t.pending_submissions > 0).length;

    return {
      totalTasks,
      activeTasks,
      completedTasks,
      pendingGrading
    };
  }, [tasks]);

  // Handle task actions
  const handleViewTask = (taskId) => {
    navigate(`/teacher/task/${taskId}`);
  };

  const handleEditTask = (taskId) => {
    navigate(`/teacher/task/${taskId}/edit`);
  };

  const handleDeleteTask = (taskId) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      // Implement delete functionality
      console.log('Delete task:', taskId);
    }
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      active: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        text: 'Active'
      },
      completed: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        text: 'Completed'
      },
      draft: {
        color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        text: 'Draft'
      }
    };

    const config = statusConfig[status] || statusConfig.draft;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  // Stats card component
  const StatsCard = ({ title, value, icon: Icon, color }) => (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`text-sm font-medium ${textSecondary}`}>{title}</p>
          <p className={`text-2xl font-bold ${textPrimary}`}>{value}</p>
        </div>
        <Icon className={`w-8 h-8 ${color}`} />
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className={`text-2xl font-bold ${textPrimary}`}>Task Management</h1>
          <p className={`mt-1 text-sm ${textSecondary}`}>
            Create, assign, and grade tasks for your students
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateTask(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <FiPlus className="w-4 h-4" />
          Create Task
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatsCard
          title="Total Tasks"
          value={stats.totalTasks}
          icon={FiBook}
          color="text-blue-600"
        />
        <StatsCard
          title="Active Tasks"
          value={stats.activeTasks}
          icon={FiClock}
          color="text-green-600"
        />
        <StatsCard
          title="Completed"
          value={stats.completedTasks}
          icon={FiCheckCircle}
          color="text-purple-600"
        />
        <StatsCard
          title="Pending Grading"
          value={stats.pendingGrading}
          icon={FiAlertCircle}
          color="text-orange-600"
        />
      </div>

      {/* Filters and Search */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4 mb-6`}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${textSecondary}`} />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="draft">Draft</option>
          </select>

          {/* Subject Filter */}
          <select
            value={subjectFilter}
            onChange={(e) => setSubjectFilter(e.target.value)}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="all">All Subjects</option>
            <option value="math">Mathematics</option>
            <option value="science">Science</option>
            <option value="english">English</option>
            <option value="history">History</option>
          </select>

          {/* Sort */}
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="created_at-desc">Newest First</option>
            <option value="created_at-asc">Oldest First</option>
            <option value="deadline-asc">Deadline (Soon)</option>
            <option value="deadline-desc">Deadline (Later)</option>
            <option value="name-asc">Name (A-Z)</option>
            <option value="name-desc">Name (Z-A)</option>
          </select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Tasks List */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor}`}>
        {/* Table Header */}
        <div className={`px-6 py-4 border-b ${borderColor} ${bgSecondary}`}>
          <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 dark:text-gray-400">
            <div className="col-span-4">Task</div>
            <div className="col-span-2">Subject</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Deadline</div>
            <div className="col-span-1">Students</div>
            <div className="col-span-1">Actions</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {loading ? (
            <div className="px-6 py-12 text-center">
              <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
              <p className={`text-sm ${textSecondary}`}>Loading tasks...</p>
            </div>
          ) : filteredTasks.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <FiBook className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
              <p className={`text-sm ${textSecondary}`}>
                {searchTerm || statusFilter !== 'all' || subjectFilter !== 'all'
                  ? 'No tasks match your filters'
                  : 'No tasks created yet'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && subjectFilter === 'all' && (
                <button
                  onClick={() => setShowCreateTask(true)}
                  className="mt-2 text-blue-600 hover:text-blue-700 underline"
                >
                  Create your first task
                </button>
              )}
            </div>
          ) : (
            filteredTasks.map((task) => (
              <div key={task.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Task Info */}
                  <div className="col-span-4">
                    <h3 className={`font-medium ${textPrimary} mb-1`}>
                      {task.name}
                    </h3>
                    <p className={`text-sm ${textSecondary} line-clamp-2`}>
                      {task.description}
                    </p>
                  </div>

                  {/* Subject */}
                  <div className="col-span-2">
                    <span className={`text-sm ${textSecondary}`}>
                      {task.subject || 'No Subject'}
                    </span>
                  </div>

                  {/* Status */}
                  <div className="col-span-2">
                    <StatusBadge status={task.status} />
                  </div>

                  {/* Deadline */}
                  <div className="col-span-2">
                    {task.deadline ? (
                      <div className={`text-sm ${textSecondary}`}>
                        <div>{new Date(task.deadline).toLocaleDateString()}</div>
                        <div className="text-xs">
                          {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                    ) : (
                      <span className={`text-sm ${textSecondary}`}>No deadline</span>
                    )}
                  </div>

                  {/* Students */}
                  <div className="col-span-1">
                    <div className="flex items-center gap-1">
                      <FiUsers className="w-4 h-4 text-gray-400" />
                      <span className={`text-sm ${textSecondary}`}>
                        {task.assigned_students || 0}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-1">
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handleViewTask(task.id)}
                        className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-blue-600`}
                        title="View Task"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => handleEditTask(task.id)}
                        className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-green-600`}
                        title="Edit Task"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => handleDeleteTask(task.id)}
                        className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-red-600`}
                        title="Delete Task"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Create Task Modal */}
      {showCreateTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CreateTask onClose={() => setShowCreateTask(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default TeacherTasks;
